'use client';

import React, { useState } from 'react';
import { ExaminationFormatUploader } from './ExaminationFormatUploader';
import { ExaminationFormatViewer } from './ExaminationFormatViewer';
import { AlertMessage } from '@/components/molecules/AlertMessage/AlertMessage';
import { Button } from '@/components/atoms/Button/Button';
import { Trash2, X, AlertTriangle } from 'lucide-react';

interface ExaminationFormatManagerProps {
  schoolId?: string;
  schoolName?: string;
  onDelete?: () => Promise<void>;
  className?: string;
}

export const ExaminationFormatManager: React.FC<ExaminationFormatManagerProps> = ({
  schoolId,
  schoolName,
  onDelete,
  className,
}) => {
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showUploader, setShowUploader] = useState(false);

  const handleDeleteClick = () => {
    setShowDeleteConfirmation(true);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirmation(false);
  };

  const handleConfirmDelete = async () => {
    if (!onDelete) return;

    setIsDeleting(true);
    setError(null);

    try {
      await onDelete();
      setSuccess('Examination format deleted successfully');
      setShowDeleteConfirmation(false);
    } catch (err) {
      setError('Failed to delete examination format');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleUploadSuccess = (data: any) => {
    setSuccess('Examination format uploaded successfully');
    setShowUploader(false); // Hide uploader after successful upload
  };

  const handleUploadError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleReplaceClick = () => {
    setShowUploader(true);
    setError(null);
    setSuccess(null);
  };

  return (
    <div className={`${className || ''}`}>
      {/* Error/Success Messages */}
      {error && <AlertMessage type="error" message={error} className="mb-4" />}
      {success && <AlertMessage type="success" message={success} className="mb-4" />}

      {/* Format Viewer */}
      <div className="mb-6">
        <ExaminationFormatViewer 
          schoolId={schoolId} 
          schoolName={schoolName}
          onReplaceClick={handleReplaceClick}
        />
      </div>

      {/* Conditional Uploader for Replacement */}
      {showUploader && (
        <div className="bg-white rounded-lg shadow-md p-6 mb-6 border border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Replace Examination Format</h3>
            <button 
              onClick={() => setShowUploader(false)}
              className="text-gray-400 hover:text-gray-600 rounded-full p-1 hover:bg-gray-100"
              aria-label="Close uploader"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          <ExaminationFormatUploader 
            schoolId={schoolId}
            onUploadSuccess={handleUploadSuccess}
            onUploadError={handleUploadError}
            isReplacement={true}
          />
        </div>
      )}

      {/* Delete Confirmation */}
      {showDeleteConfirmation ? (
        <div className="bg-white rounded-lg border border-red-200 p-6 shadow-md">
          <div className="flex items-start mb-4">
            <div className="bg-red-100 p-2 rounded-full mr-3">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-red-600 mb-1">Delete Examination Format</h3>
              <p className="text-gray-600 mb-4">
                Are you sure you want to delete this examination format? This action cannot be undone.
              </p>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={handleCancelDelete}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="error"
              onClick={handleConfirmDelete}
              disabled={isDeleting}
              isLoading={isDeleting}
            >
              Delete Format
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex justify-end">
          <Button
            variant="error"
            onClick={handleDeleteClick}
            className="!w-auto"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete Format
          </Button>
        </div>
      )}
    </div>
  );
};